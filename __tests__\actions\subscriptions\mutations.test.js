// __tests__/actions/subscriptions/mutations.test.js

// Test the company name extraction logic directly
function extractCompanyNameFromWebsite(website) {
  if (!website?.trim()) return "Unknown Company";

  try {
    let domain = website.trim();
    // Remove protocol if present
    domain = domain.replace(/^https?:\/\//, '');
    // Remove www. if present
    domain = domain.replace(/^www\./, '');
    // Remove path and query parameters
    domain = domain.split('/')[0].split('?')[0];
    // Capitalize first letter and remove extension
    const companyName = domain.split('.')[0];
    return companyName.charAt(0).toUpperCase() + companyName.slice(1);
  } catch (error) {
    return "Unknown Company";
  }
}

describe("Company Name Extraction", () => {
  describe("extractCompanyNameFromWebsite", () => {
    it("should extract company name from simple domain", () => {
      expect(extractCompanyNameFromWebsite("nzbgeek.info")).toBe("Nzbgeek");
      expect(extractCompanyNameFromWebsite("spotify.com")).toBe("Spotify");
      expect(extractCompanyNameFromWebsite("netflix.com")).toBe("Netflix");
    });

    it("should handle domains with www prefix", () => {
      expect(extractCompanyNameFromWebsite("www.example.com")).toBe("Example");
      expect(extractCompanyNameFromWebsite("www.github.com")).toBe("Github");
    });

    it("should handle domains with protocol", () => {
      expect(extractCompanyNameFromWebsite("https://example.com")).toBe("Example");
      expect(extractCompanyNameFromWebsite("http://test.org")).toBe("Test");
    });

    it("should handle domains with paths", () => {
      expect(extractCompanyNameFromWebsite("example.com/path/to/page")).toBe("Example");
      expect(extractCompanyNameFromWebsite("test.com?query=param")).toBe("Test");
    });

    it("should handle complex URLs", () => {
      expect(extractCompanyNameFromWebsite("https://www.example.com/path?query=1")).toBe("Example");
    });

    it("should return fallback for empty or invalid input", () => {
      expect(extractCompanyNameFromWebsite("")).toBe("Unknown Company");
      expect(extractCompanyNameFromWebsite(null)).toBe("Unknown Company");
      expect(extractCompanyNameFromWebsite(undefined)).toBe("Unknown Company");
      expect(extractCompanyNameFromWebsite("   ")).toBe("Unknown Company");
    });
  });

});
