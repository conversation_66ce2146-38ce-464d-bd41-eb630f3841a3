// /utils/hooks/useAnalytics.js
import { useQuery } from "@tanstack/react-query";
import {
  getSubscriptionSpendingTrends,
  getUpcomingRenewals,
  getSubscriptionCategories,
  getPaymentMethods,
  getYTDSpending,
  getPriceChanges,
} from "@/utils/subscription-analytics";
import { useProfile } from "./useProfile";

export function useSpendingTrends() {
  const { data: profile } = useProfile();

  return useQuery({
    queryKey: ["spendingTrends", profile?.user_id],
    queryFn: () => getSubscriptionSpendingTrends(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    enabled: Boolean(profile?.user_id),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    placeholderData: (previousData) => previousData,
  });
}

export function useUpcomingRenewals(days = 30) {
  const { data: profile } = useProfile();

  return useQuery({
    queryKey: ["upcomingRenewals", days, profile?.user_id],
    queryFn: () => getUpcomingRenewals(days),
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
    enabled: Boolean(profile?.user_id),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    placeholderData: (previousData) => previousData,
  });
}

export function useSubscriptionCategories() {
  const { data: profile } = useProfile();

  return useQuery({
    queryKey: ["subscriptionCategories", profile?.user_id],
    queryFn: () => getSubscriptionCategories(),
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
    enabled: Boolean(profile?.user_id),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    placeholderData: (previousData) => previousData,
  });
}

export function usePaymentMethods() {
  const { data: profile } = useProfile();

  return useQuery({
    queryKey: ["paymentMethods", profile?.user_id],
    queryFn: () => getPaymentMethods(),
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
    enabled: Boolean(profile?.user_id),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    placeholderData: (previousData) => previousData,
  });
}

export function useYTDSpending() {
  const { data: profile } = useProfile();

  return useQuery({
    queryKey: ["ytdSpending", profile?.user_id],
    queryFn: () => getYTDSpending(),
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
    enabled: Boolean(profile?.user_id),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    placeholderData: (previousData) => previousData,
  });
}

export function usePriceChanges() {
  const { data: profile } = useProfile();

  return useQuery({
    queryKey: ["priceChanges", profile?.user_id],
    queryFn: () => getPriceChanges(),
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
    enabled: Boolean(profile?.user_id),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    placeholderData: (previousData) => previousData,
  });
}
